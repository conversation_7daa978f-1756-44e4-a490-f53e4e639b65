package grpcstarter.example;

import org.springframework.web.bind.annotation.*;
import test.User;
import test.TestParameterRequest;
import test.TestParameterResponse;
import test.TestBigMessageRequest;
import test.TestBigMessageResponse;
import test.TestBodyNotStarRequest;
import test.TestBodyNotStarResponse;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.time.LocalDateTime;
import java.math.BigDecimal;

@RestController
public class Controller {

    @PostMapping("/hello")
    public User hello(@RequestBody User user) {
        return user;
    }

    // ========== Java Bean 嵌套 protobuf message 测试用例 ==========

    /**
     * Java POJO 包含 protobuf User 消息
     */
    public static class UserWrapper {
        private String wrapperName;
        private User user;
        private LocalDateTime createdAt;
        private boolean isActive;

        // Getters and Setters
        public String getWrapperName() { return wrapperName; }
        public void setWrapperName(String wrapperName) { this.wrapperName = wrapperName; }
        public User getUser() { return user; }
        public void setUser(User user) { this.user = user; }
        public LocalDateTime getCreatedAt() { return createdAt; }
        public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
        public boolean isActive() { return isActive; }
        public void setActive(boolean active) { isActive = active; }
    }

    @PostMapping("/api/user-wrapper")
    public UserWrapper createUserWrapper(@RequestBody UserWrapper userWrapper) {
        return userWrapper;
    }

    /**
     * Java POJO 包含 protobuf PhoneNumber 嵌套消息
     */
    public static class ContactInfo {
        private String contactId;
        private User.PhoneNumber primaryPhone;
        private List<User.PhoneNumber> additionalPhones;
        private String notes;

        // Getters and Setters
        public String getContactId() { return contactId; }
        public void setContactId(String contactId) { this.contactId = contactId; }
        public User.PhoneNumber getPrimaryPhone() { return primaryPhone; }
        public void setPrimaryPhone(User.PhoneNumber primaryPhone) { this.primaryPhone = primaryPhone; }
        public List<User.PhoneNumber> getAdditionalPhones() { return additionalPhones; }
        public void setAdditionalPhones(List<User.PhoneNumber> additionalPhones) { this.additionalPhones = additionalPhones; }
        public String getNotes() { return notes; }
        public void setNotes(String notes) { this.notes = notes; }
    }

    @PostMapping("/api/contact-info")
    public ContactInfo createContactInfo(@RequestBody ContactInfo contactInfo) {
        return contactInfo;
    }

    /**
     * Java POJO 包含 protobuf Address 嵌套消息
     */
    public static class LocationInfo {
        private String locationId;
        private User.Address address;
        private Double latitude;
        private Double longitude;
        private Set<String> tags;

        // Getters and Setters
        public String getLocationId() { return locationId; }
        public void setLocationId(String locationId) { this.locationId = locationId; }
        public User.Address getAddress() { return address; }
        public void setAddress(User.Address address) { this.address = address; }
        public Double getLatitude() { return latitude; }
        public void setLatitude(Double latitude) { this.latitude = latitude; }
        public Double getLongitude() { return longitude; }
        public void setLongitude(Double longitude) { this.longitude = longitude; }
        public Set<String> getTags() { return tags; }
        public void setTags(Set<String> tags) { this.tags = tags; }
    }

    @PutMapping("/api/location/{locationId}")
    public LocationInfo updateLocation(@PathVariable String locationId, @RequestBody LocationInfo locationInfo) {
        locationInfo.setLocationId(locationId);
        return locationInfo;
    }

    // ========== Java Bean 嵌套 protobuf enum 测试用例 ==========

    /**
     * Java POJO 包含 protobuf Gender 枚举
     */
    public static class UserProfile {
        private String profileId;
        private String displayName;
        private User.Gender gender;
        private Integer age;
        private List<User.Gender> allowedGenders;
        private Map<String, User.Gender> genderPreferences;

        // Getters and Setters
        public String getProfileId() { return profileId; }
        public void setProfileId(String profileId) { this.profileId = profileId; }
        public String getDisplayName() { return displayName; }
        public void setDisplayName(String displayName) { this.displayName = displayName; }
        public User.Gender getGender() { return gender; }
        public void setGender(User.Gender gender) { this.gender = gender; }
        public Integer getAge() { return age; }
        public void setAge(Integer age) { this.age = age; }
        public List<User.Gender> getAllowedGenders() { return allowedGenders; }
        public void setAllowedGenders(List<User.Gender> allowedGenders) { this.allowedGenders = allowedGenders; }
        public Map<String, User.Gender> getGenderPreferences() { return genderPreferences; }
        public void setGenderPreferences(Map<String, User.Gender> genderPreferences) { this.genderPreferences = genderPreferences; }
    }

    @PostMapping("/api/user-profile")
    public UserProfile createUserProfile(@RequestBody UserProfile userProfile) {
        return userProfile;
    }

    @GetMapping("/api/user-profile/{profileId}")
    public UserProfile getUserProfile(@PathVariable String profileId,
                                    @RequestParam(required = false) User.Gender filterGender) {
        UserProfile profile = new UserProfile();
        profile.setProfileId(profileId);
        profile.setGender(filterGender);
        return profile;
    }
}

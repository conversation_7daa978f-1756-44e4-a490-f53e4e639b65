plugins {
    id "org.springframework.boot"
}

dependencies {
    implementation("io.grpc:grpc-testing-proto")
    implementation(project(":grpc-starters:grpc-server-boot-starter")){
        exclude(group: 'io.grpc', module: "grpc-netty-shaded")
    }
    implementation(project(":grpc-starters:grpc-starter-transcoding"))
    implementation(project(":grpc-starters:grpc-starter-transcoding-springdocs"))
    implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:${springDocsVersion}")
    implementation("org.springframework.boot:spring-boot-starter-web")
    runtimeOnly("io.grpc:grpc-netty")

    testImplementation(project(":grpc-starters:grpc-starter-test"))
}

apply from: "${rootDir}/gradle/protobuf.gradle"

dependencies {
    compileOnly(project(":grpc-boot-autoconfigure:grpc-server-boot-autoconfigure"))
    api(project(":grpc-extensions:grpc-transcoding"))

    compileOnly("org.springdoc:springdoc-openapi-starter-common:${springDocsVersion}")

    compileOnly("org.springframework.boot:spring-boot-starter-web")
    compileOnly("org.springframework.boot:spring-boot-starter-webflux")

    testImplementation(project(":grpc-starters:grpc-boot-starter"))
    testImplementation(project(":grpc-starters:grpc-starter-test"))
    testImplementation("org.springframework.boot:spring-boot-starter-web")
    testImplementation("org.springdoc:springdoc-openapi-starter-webmvc-api:${springDocsVersion}")
}

apply from: "${rootDir}/gradle/deploy.gradle"
